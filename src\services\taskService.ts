import type { TaskData, TaskFormData, TaskSearchParams, TaskApiResponse } from '../types/task';

/**
 * 生成模拟任务数据
 */
function generateMockTasks(count: number): TaskData[] {
  const tasks: TaskData[] = [];
  const groups = ['系统维护', '数据备份', '监控告警', '日志清理', '性能优化'];
  const frequencies = ['daily', 'weekly', 'monthly', 'custom'];
  const statuses: ('active' | 'inactive' | 'pending')[] = ['active', 'inactive', 'pending'];
  const alertTriggers = ['失败时', '成功时', '总是', '从不'];
  const receivers = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

  for (let i = 1; i <= count; i++) {
    const startHour = Math.floor(Math.random() * 24);
    const startMinute = Math.floor(Math.random() * 60);
    const endHour = (startHour + Math.floor(Math.random() * 3) + 1) % 24;
    const endMinute = Math.floor(Math.random() * 60);
    
    tasks.push({
      id: i,
      name: `任务${i.toString().padStart(3, '0')}`,
      group: groups[Math.floor(Math.random() * groups.length)],
      start_time: `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}:00`,
      end_time: `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}:00`,
      weekday: Math.random() > 0.5 ? 
        Math.floor(Math.random() * 7 + 1).toString() : 
        '1,2,3,4,5',
      frequency: frequencies[Math.floor(Math.random() * frequencies.length)],
      retryNum: Math.floor(Math.random() * 5).toString(),
      retry_frequency: `${Math.floor(Math.random() * 60 + 1)}分钟`,
      alert_trigger: alertTriggers[Math.floor(Math.random() * alertTriggers.length)],
      alert_receiver: receivers[Math.floor(Math.random() * receivers.length)],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString(),
      status: statuses[Math.floor(Math.random() * statuses.length)],
    });
  }

  return tasks;
}

/**
 * 模拟数据 - 生成1000条任务数据
 */
export const mockTaskData: TaskData[] = generateMockTasks(1000);

/**
 * 任务API服务类
 */
export class TaskService {
  /**
   * 获取任务列表
   * @param params 搜索参数
   * @returns 任务列表和总数
   */
  static async getTasks(params: TaskSearchParams): Promise<TaskApiResponse<TaskData>> {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 模拟分页和搜索逻辑
    let filteredData = [...mockTaskData];
    
    // 根据搜索条件过滤数据
    if (params.name) {
      filteredData = filteredData.filter(item => 
        item.name.includes(params.name!)
      );
    }
    
    if (params.group) {
      filteredData = filteredData.filter(item => 
        item.group.includes(params.group!)
      );
    }

    if (params.status) {
      filteredData = filteredData.filter(item => 
        item.status === params.status
      );
    }

    if (params.weekday) {
      filteredData = filteredData.filter(item => 
        item.weekday.includes(params.weekday!)
      );
    }

    if (params.frequency) {
      filteredData = filteredData.filter(item => 
        item.frequency === params.frequency
      );
    }

    if (params.alert_receiver) {
      filteredData = filteredData.filter(item => 
        item.alert_receiver.includes(params.alert_receiver!)
      );
    }

    // 分页处理
    const { current = 1, pageSize = 10 } = params;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: filteredData.length,
      success: true,
    };
  }

  /**
   * 删除单个任务
   * @param id 任务ID
   * @returns 是否删除成功
   */
  static async deleteTask(id: number): Promise<boolean> {
    console.log("删除任务:", id);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    return true; // 模拟成功
  }

  /**
   * 批量删除任务
   * @param ids 任务ID数组
   * @returns 是否删除成功
   */
  static async batchDeleteTasks(ids: number[]): Promise<boolean> {
    console.log("批量删除任务:", ids);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 800));
    
    return true; // 模拟成功
  }

  /**
   * 添加任务
   * @param taskData 任务数据
   * @returns 新创建的任务信息
   */
  static async addTask(taskData: TaskFormData): Promise<TaskData> {
    console.log("添加任务:", taskData);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 600));
    
    // 模拟返回新创建的任务
    const newTask: TaskData = {
      id: Date.now(),
      ...taskData,
      createTime: new Date().toLocaleString(),
    };
    
    return newTask;
  }

  /**
   * 更新任务
   * @param id 任务ID
   * @param taskData 更新的任务数据
   * @returns 更新后的任务信息
   */
  static async updateTask(id: number, taskData: Partial<TaskFormData>): Promise<TaskData> {
    console.log("更新任务:", id, taskData);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    // 模拟返回更新后的任务
    const existingTask = mockTaskData.find(task => task.id === id);
    if (!existingTask) {
      throw new Error('任务不存在');
    }
    
    return {
      ...existingTask,
      ...taskData,
    };
  }
}
