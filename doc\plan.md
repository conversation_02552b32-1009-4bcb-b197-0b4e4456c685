# ProTable 示例开发计划

## Notes
- 用户要求分步编写 antd ProTable 示例，需用户确认后继续
- 项目已安装 antd 5.x 和 @ant-design/pro-components 2.x
- 前端包管理器为 pnpm，前端语言为 TypeScript，需保持代码注释为中文
- 目标文件 src/components/ProTable.tsx 已存在
- 发现“编辑弹窗无法打开”问题，根因是 handleEdit/handleDelete 在函数体内重复声明，需移除多余定义，仅保留一份并确保在 columns 定义前声明。
- 用户要求实现删除操作：点击删除时气泡弹窗确认，确认后调用删除API，并支持两种表格数据更新方案（1. 重新请求数据并渲染 2. 直接更新表格数据）。
- actionRef 用于表格刷新，需指定类型避免类型警告。
- 用户要求新增查询功能。
- 年龄字段需支持范围查询，但表格中只显示单个年龄值。
- 用户要求姓名和地址字段支持排序。
- 用户要求创建时间字段支持排序。
- 用户要求将查询的展开/收起按钮移到表格工具栏，按钮外观为放大镜图标。
- 已切换为 ProTable 官方工具栏查询展开/收起按钮，移除自定义 span 及相关状态。
- 已根据用户需求回退到自定义 span 查询展开/收起按钮，并优化其样式与 ProTable 官方工具栏按钮完全一致。
- 用户新增需求：表格支持多选，选中项跨页不失效，且在表格与工具栏之间显示已选提示行，支持一键取消所有选择。
- 多选提示行应位于查询表单与表格之间，而非工具栏内部。
- 已实现多选提示行位于查询表单和表格之间的布局。
- 分页 pageSize 更改时表格未刷新的问题已修复。
- 修复 handleSearch 未分页返回数据导致分页无效。
- 已修复 ProTable 异步分页警告，data/total/pagination 配置已完全匹配。
- 用户新增需求：分页 pageSize 或 current 变化时，多选 key 依旧保持有效，不自动清除。
- 用户新增需求：禁用 ProTable 自带多选提示行，仅保留自定义 SelectionAlert。
- 用户新增需求：批量删除按钮，仅在有多选时显示，弹窗确认后调用API批量删除并刷新表格。
- 已优化批量删除为Modal确认，支持展示详细用户信息，修正类型错误。
- 已修复 Antd 5.x message 静态方法上下文警告（已用 App 组件包裹应用）。
- 但发现 message 依旧无法正常执行，需进一步兼容 Antd 5.x 新用法（如 message hooks）。
- 新需求：重置按钮、查询按钮、添加用户按钮、工具栏刷新时需自动取消多选和多选提示。
- 新增需求：ProTable表格工具栏-列设置改为自定义抽屉，功能不变，样式优化，抽屉右下角确认和重置，点击抽屉外区域自动退出并重置，分步执行。
  - 已分析现有 ProTableDemo 代码，确认已具备列设置抽屉相关状态和部分实现。
  - [x] 第一步：实现抽屉弹出与关闭逻辑，展示列配置项
  - [x] 第二步：实现列显示/隐藏切换、临时状态与确认/重置按钮（需实现初始快照、确认、重置和关闭逻辑，保证抽屉关闭或取消时临时设置恢复，确认时才生效）
  - [ ] 第三步：抽屉外点击自动关闭并重置
  - [ ] 第四步：样式优化与交互完善

## Task List
- [x] 检查 antd 及相关依赖是否已安装
- [x] 指导用户安装 @ant-design/pro-components 并确认安装完成
- [x] 确认 ProTable.tsx 文件已存在
- [x] 编写基础 ProTable 示例代码
- [x] 为 ProTable 示例添加中文注释
- [x] 逐步完善 ProTable 示例功能
  - [x] 实现编辑弹窗（使用 Drawer 组件，右侧打开，支持取消/确认/重置，遮罩关闭前弹窗确认）
  - [x] 修复编辑弹窗无法打开及相关交互BUG
  - [x] 实现删除操作（气泡确认+API+两种数据更新方式）
  - [x] 修复 actionRef 类型警告
  - [x] 增加查询功能
  - [x] 优化年龄范围查询与表格显示分离
  - [x] 增加姓名和地址字段排序
  - [x] 增加创建时间字段排序
  - [x] 增加新增/编辑表单弹窗
  - [ ] 接入真实后端 API
  - [ ] 增加更多搜索条件与筛选功能
  - [x] 优化表格样式和用户体验
    - [x] 修复工具栏刷新/查询按钮变宽问题
    - [x] 查询展开/收起按钮移到工具栏，并使用放大镜图标
    - [x] 查询展开/收起按钮隐藏，toolBarRender 使用 span 并与工具栏官方按钮风格一致
    - [x] 查询展开/收起按钮采用 ProTable 官方工具栏按钮方案
    - [x] 修复查询操作按钮（重置/查询）与输入框的水平对齐问题
  - [x] 实现表格多选及已选提示行（支持跨页选择、取消所有选择）
  - [x] 修复多选提示行显示位置（移至查询表单和表格之间）
  - [x] 修复切换分页 pageSize 时表格未变化的问题
  - [x] 修复 handleSearch 未分页返回数据导致分页无效
  - [x] 修复 ProTable 异步分页警告，data/total/pagination 配置完全匹配
  - [x] 支持分页 pageSize 或 current 变化时多选 key 保持有效
  - [x] 禁用 ProTable 自带多选提示行，仅保留自定义 SelectionAlert
  - [x] 工具栏显示批量删除按钮（仅多选时出现，点击弹窗确认）
  - [x] 实现批量删除API及表格刷新（等待用户命令继续）
  - [x] 优化批量删除按钮交互为Modal确认
  - [x] 修复 Antd 5.x message 静态方法上下文警告（已尝试App组件包裹）
  - [ ] 尝试使用 message hooks 方式修复 Antd 5.x message 上下文问题
  - [ ] 重置、查询、添加用户、工具栏刷新时自动取消多选和多选提示
  - [x] 实现自定义列设置抽屉（第一步）
  - [x] 实现列显示/隐藏切换、临时状态与确认/重置按钮（第二步，需实现初始快照、确认、重置和关闭逻辑）
  - [ ] 实现抽屉外点击自动关闭并重置（第三步）

## Current Goal
- 实现抽屉外点击自动关闭并重置（第三步）