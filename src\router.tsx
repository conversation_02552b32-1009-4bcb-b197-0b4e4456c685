import { createBrowserRouter } from "react-router-dom";
import { ResponsiveLayout } from "./layouts/ResponsiveLayout";
import ProTableRefactored from "./components/ProTableRefactored";
import TestRefactored from "./components/TestRefactored";
import VTable from "./components/VTable";
import TableWithIndeterminate from "./components/TableWithIndeterminate";
import VTableConfigComparison from "./components/VTableConfigComparison";
import VTableTest from "./components/VTableTest";
import VTableSimple from "./components/VTableSimple";
import VTableEmptyStateTest from "./components/VTableEmptyStateTest";
import TaskTablePage from "./pages/TaskTablePage";
import AntdTable from "./components/AntdTable";

export const router = createBrowserRouter([
  {
    path: "/",
    Component: ResponsiveLayout,
    children: [
      {
        index: true,
        Component: () => <ProTableRefactored />,
      },
      {
        path: "protable",
        Component: () => <ProTableRefactored />,
      },
      {
        path: "test",
        Component: TestRefactored,
      },
      {
        path: "task-table",
        Component: () => <TaskTablePage />,
      },
    ],
  },
  {
    path: "*",
    Component: () => <div>404 Not Found</div>,
  },
  {
    path: "/vtable",
    Component: () => <VTable />,
  },
  {
    path: "/table",
    Component: () => <TableWithIndeterminate />,
  },
  {
    path: "/vtable-config",
    Component: () => <VTableConfigComparison />,
  },
  {
    path: "/vtable-test",
    Component: () => <VTableTest />,
  },
  {
    path: "/vtable-simple",
    Component: () => <VTableSimple />,
  },
  {
    path: "/vtable-empty-test",
    Component: () => <VTableEmptyStateTest />,
  },
  {
    path: "/task-management",
    Component: () => <TaskTablePage />,
  },
  {
    path: "/antd/table",
    Component: () => <AntdTable />,
  },
]);
