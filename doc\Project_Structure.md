# 项目结构文档

## 📋 项目基本信息

- **前端框架**: Vite + TypeScript
- **包管理器**: pnpm
- **UI组件库**: Ant Design 5.x
- **表格组件**: @ant-design/pro-components 2.x
- **代码注释**: 中文

## 🏗️ 项目目录结构

```
my-react-app/
├── src/
│   ├── components/
│   │   └── ProTable.tsx              # 主要的ProTable组件
│   ├── App.tsx                       # 应用主组件
│   └── main.tsx                      # 应用入口文件
├── doc/
│   ├── ProTable 1.md                 # ProTable开发记录
│   ├── plan.md                       # 开发计划文档
│   ├── ProTable_Architecture_Discussion.md  # 架构设计讨论
│   └── Project_Structure.md          # 项目结构文档（本文件）
├── package.json                      # 项目依赖配置
├── pnpm-lock.yaml                    # pnpm锁定文件
├── vite.config.ts                    # Vite配置文件
└── tsconfig.json                     # TypeScript配置文件
```

## 📦 核心依赖

### 主要依赖
- **React 18.x**: 前端框架
- **TypeScript**: 类型安全
- **Vite**: 构建工具
- **Ant Design 5.x**: UI组件库
- **@ant-design/pro-components 2.x**: 高级组件库

### 开发依赖
- **@types/react**: React类型定义
- **@types/react-dom**: ReactDOM类型定义
- **@vitejs/plugin-react**: Vite React插件

## 🎯 核心组件说明

### ProTable.tsx
位置: `src/components/ProTable.tsx`

**主要功能**:
- 表格数据展示和分页
- 查询表单（姓名、年龄范围、地址、标签、创建时间）
- 多选功能（支持跨页选择）
- 编辑功能（右侧抽屉）
- 删除功能（单个删除和批量删除）
- 工具栏功能（刷新、密度、列设置、查询展开/收起）
- 排序功能（姓名、地址、创建时间）

**技术特点**:
- 使用Ant Design的App组件解决message上下文问题
- 支持自定义工具栏按钮
- 响应式设计
- TypeScript类型安全

### App.tsx
位置: `src/App.tsx`

**主要功能**:
- 应用根组件
- 使用Ant Design App组件提供全局上下文
- 包裹ProTable组件

## 📝 文档说明

### ProTable 1.md
记录了ProTable组件的完整开发过程，包括：
- 功能实现步骤
- 问题解决方案
- 代码修改记录

### plan.md
项目开发计划文档，包括：
- 任务清单
- 开发进度
- 技术要求
- 已知问题和解决方案

### ProTable_Architecture_Discussion.md
架构设计讨论文档，包括：
- 重构架构设计
- 自定义hooks说明
- 通用组件介绍
- 文件组织结构

## 🔧 开发环境配置

### 启动开发服务器
```bash
pnpm run dev
```

### 构建生产版本
```bash
pnpm run build
```

### 预览生产版本
```bash
pnpm run preview
```

## 🎨 代码规范

- **组件命名**: 使用PascalCase
- **文件命名**: 使用PascalCase（组件文件）
- **变量命名**: 使用camelCase
- **注释语言**: 中文
- **类型定义**: 使用TypeScript接口和类型

## 📋 功能特性

### ✅ 已实现功能
- [x] 基础表格展示
- [x] 分页功能
- [x] 查询功能
- [x] 多选功能（跨页保持）
- [x] 编辑功能（抽屉形式）
- [x] 删除功能（单个和批量）
- [x] 排序功能
- [x] 工具栏自定义
- [x] 响应式设计
- [x] TypeScript类型安全

### 🔄 待优化功能
- [ ] 真实API接入
- [ ] 更多查询条件
- [ ] 数据导出功能
- [ ] 表格列自定义
- [ ] 主题切换支持

## 🐛 已解决问题

1. **编辑弹窗无法打开**: 修复函数重复声明问题
2. **actionRef类型警告**: 添加正确的类型定义
3. **分页数据不刷新**: 修复handleSearch分页逻辑
4. **message上下文警告**: 使用App组件包裹解决
5. **多选状态跨页丢失**: 实现选择状态持久化
6. **工具栏按钮样式不一致**: 统一按钮样式规范

---

**文档更新时间**: ${new Date().toLocaleString('zh-CN')}
**文件路径**: `doc/Project_Structure.md`