/**
 * 任务数据项类型定义
 * 用于定义表格中每一行任务数据的类型
 */
export interface TaskData {
  id: number;
  /** 任务名称 */
  name: string;
  /** 任务分组 */
  group: string;
  /** 开始时间, 格式为HH:mm:ss */
  start_time: string;
  /** 结束时间, 格式为HH:mm:ss */
  end_time: string;
  /** 星期，格式 1-7 / 1,2,3,4,5 */
  weekday: string;
  /** 执行频率 */
  frequency: string;
  /** 重试次数 */
  retryNum: string;
  /** 重试间隔 */
  retry_frequency: string;
  /** 告警触发条件 */
  alert_trigger: string;
  /** 告警接收人 */
  alert_receiver: string;
  /** 创建时间 */
  createTime: string;
  /** 状态 */
  status: 'active' | 'inactive' | 'pending';
}

/**
 * 任务表单数据类型（不包含id和createTime）
 */
export type TaskFormData = Omit<TaskData, 'id' | 'createTime'>;

/**
 * 搜索参数类型
 */
export type TaskSearchParams = {
  current?: number;
  pageSize?: number;
  name?: string;
  group?: string;
  status?: 'active' | 'inactive' | 'pending';
  weekday?: string;
  frequency?: string;
  start_time?: string;
  end_time?: string;
  alert_trigger?: string;
  alert_receiver?: string;
  retryNum?: string;
  retry_frequency?: string;
};

/**
 * API响应类型
 */
export type TaskApiResponse<T> = {
  data: T[];
  total: number;
  success: boolean;
  message?: string;
};

/**
 * 表格选择状态类型
 */
export type TaskSelectionState = {
  selectedRowKeys: React.Key[];
  selectedRows: TaskData[];
};

/**
 * Modal状态类型
 */
export type TaskModalState = {
  visible: boolean;
  loading?: boolean;
};

/**
 * 抽屉状态类型
 */
export type TaskDrawerState = {
  visible: boolean;
  loading?: boolean;
};

/**
 * 表单操作类型
 */
export type TaskFormAction = 'add' | 'edit';

/**
 * 更新方式类型
 */
export type TaskUpdateMethod = 'reload' | 'direct';

/**
 * 任务状态选项
 */
export const TASK_STATUS_OPTIONS = [
  { label: '激活', value: 'active' },
  { label: '未激活', value: 'inactive' },
  { label: '待处理', value: 'pending' },
] as const;

/**
 * 星期选项
 */
export const WEEKDAY_OPTIONS = [
  { label: '周一', value: '1' },
  { label: '周二', value: '2' },
  { label: '周三', value: '3' },
  { label: '周四', value: '4' },
  { label: '周五', value: '5' },
  { label: '周六', value: '6' },
  { label: '周日', value: '7' },
] as const;

/**
 * 频率选项
 */
export const FREQUENCY_OPTIONS = [
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' },
  { label: '自定义', value: 'custom' },
] as const;
