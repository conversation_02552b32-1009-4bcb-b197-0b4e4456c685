{"globals": {"Link": true, "NavLink": true, "Navigate": true, "Outlet": true, "ProTable": true, "Route": true, "Routes": true, "Table": true, "createRef": true, "forwardRef": true, "lazy": true, "memo": true, "startTransition": true, "useCallback": true, "useContext": true, "useDebugValue": true, "useDeferredValue": true, "useEffect": true, "useHref": true, "useId": true, "useImperativeHandle": true, "useInRouterContext": true, "useInsertionEffect": true, "useLayoutEffect": true, "useLinkClickHandler": true, "useLocation": true, "useMemo": true, "useNavigate": true, "useNavigationType": true, "useOutlet": true, "useOutletContext": true, "useParams": true, "useReducer": true, "useRef": true, "useResolvedPath": true, "useRoutes": true, "useSearchParams": true, "useState": true, "useSyncExternalStore": true, "useTransition": true}}