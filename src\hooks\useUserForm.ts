import { useCallback } from "react";
import { Form, type FormInstance } from "antd";
import type { MessageInstance } from "antd/es/message/interface";
import type { DataItem, UserFormData } from "../types/user";

// 定义确认对话框返回类型
interface ConfirmDialogReturn {
  needConfirm: boolean;
  confirmTitle?: string;
  confirmContent?: string;
  onConfirm?: () => void;
}

// 定义表单验证结果类型
type FormValidationResult<T> =
  | { success: true; values: T }
  | { success: false; error: Error };
import { UserService } from "../services/userService";

/**
 * 用户表单管理Hook
 * 提供表单的增删改查操作
 */
export const useUserForm = (messageApi?: MessageInstance) => {
  const [form] = Form.useForm<UserFormData>();
  const [editForm] = Form.useForm<UserFormData>();

  // 创建一个安全的 message 函数
  const showMessage = useCallback(
    (type: "success" | "error", content: string) => {
      if (messageApi) {
        messageApi[type](content);
      } else {
        // 如果没有传入 messageApi，使用 console 作为后备
        console.log(`[${type.toUpperCase()}] ${content}`);
      }
    },
    [messageApi]
  );

  /**
   * 重置表单
   */
  const resetForm = useCallback(
    (formInstance: FormInstance = form) => {
      formInstance.resetFields();
    },
    [form]
  );

  /**
   * 设置表单值
   */
  const setFormValues = useCallback(
    (values: Partial<DataItem>, formInstance: FormInstance = form) => {
      formInstance.setFieldsValue(values);
    },
    [form]
  );

  /**
   * 检查表单是否被修改
   */
  const isFormTouched = useCallback(
    (formInstance: FormInstance = form) => {
      return formInstance.isFieldsTouched();
    },
    [form]
  );

  /**
   * 验证表单并获取值
   */
  const validateAndGetValues = useCallback(
    async (
      formInstance: FormInstance = form
    ): Promise<FormValidationResult<UserFormData>> => {
      try {
        const values = await formInstance.validateFields();
        return { success: true, values };
      } catch (error) {
        console.error("表单验证失败:", error);
        const normalizedError =
          error instanceof Error ? error : new Error(String(error));
        return { success: false, error: normalizedError };
      }
    },
    [form]
  );

  /**
   * 提交添加用户表单
   */
  const submitAddForm = useCallback(async (): Promise<{
    success: boolean;
    data?: DataItem;
  }> => {
    const result = await validateAndGetValues(form);
    if (!result.success) {
      return { success: false };
    }

    try {
      const newUser = await UserService.addUser(result.values);
      showMessage("success", `用户 ${newUser.name} 添加成功`);
      resetForm(form);
      return { success: true, data: newUser };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "添加失败";
      showMessage("error", `添加用户失败: ${errorMessage}`);
      return { success: false };
    }
  }, [form, validateAndGetValues, resetForm, showMessage]);

  /**
   * 提交编辑用户表单
   */
  const submitEditForm = useCallback(
    async (userId: string): Promise<{ success: boolean; data?: DataItem }> => {
      const result = await validateAndGetValues(editForm);
      if (!result.success) {
        return { success: false };
      }

      try {
        const updatedUser = await UserService.updateUser(userId, result.values);
        showMessage("success", `用户 ${updatedUser.name} 更新成功`);
        return { success: true, data: updatedUser };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "更新失败";
        showMessage("error", `更新用户失败: ${errorMessage}`);
        return { success: false };
      }
    },
    [editForm, validateAndGetValues, showMessage]
  );

  /**
   * 准备编辑表单
   */
  const prepareEditForm = useCallback(
    (record: DataItem) => {
      const formValues = {
        name: record.name,
        age: record.age,
        address: record.address,
        tags: record.tags,
      };
      // 重置表单并设置新值
      editForm.resetFields();
      editForm.setFieldsValue(formValues);
      // 使用 setTimeout 确保在下一个事件循环中重置 touched 状态
      setTimeout(() => {
        editForm.resetFields();
        editForm.setFieldsValue(formValues);
      }, 0);
    },
    [editForm]
  );

  /**
   * 处理表单取消操作（带确认）
   */
  const handleFormCancel = useCallback(
    (
      onConfirm: () => void,
      formInstance: FormInstance = form,
      confirmTitle = "确认取消",
      confirmContent = "表单内容未保存，确定要取消吗？"
    ): ConfirmDialogReturn => {
      if (isFormTouched(formInstance)) {
        // 如果表单被修改过，显示确认对话框
        return {
          needConfirm: true,
          confirmTitle,
          confirmContent,
          onConfirm: () => {
            resetForm(formInstance);
            onConfirm();
          },
        };
      } else {
        // 如果表单未被修改，直接取消
        onConfirm();
        return { needConfirm: false };
      }
    },
    [isFormTouched, resetForm]
  );

  return {
    form,
    editForm,
    resetForm,
    setFormValues,
    isFormTouched,
    validateAndGetValues,
    submitAddForm,
    submitEditForm,
    prepareEditForm,
    handleFormCancel,
  };
};


